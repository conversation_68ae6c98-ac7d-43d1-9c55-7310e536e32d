<?php
include 'database.php';

$id = isset($_GET['id']) ? $_GET['id'] : '';
$jurusan = null;

if ($id) {
    // Fetch jurusan
    $stmt = $conn->prepare('SELECT id_jurusan, kode_jurusan, nama_jurusan FROM jurusan WHERE id_jurusan = ?');
    $stmt->bind_param('s', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $row = $result->fetch_assoc()) {
        $jurusan = $row;
    }
    $stmt->close();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = $_POST['id_jurusan'];
    $kode = $_POST['kode_jurusan'];
    $nama = $_POST['nama_jurusan'];

    // Debug: Log POST data
    error_log('FORM POST DATA: ' . print_r($_POST, true));

    // Direct update instead of CURL
    $sql = 'UPDATE jurusan SET kode_jurusan=?, nama_jurusan=? WHERE id_jurusan=?';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sss', $kode, $nama, $id);

    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo "<script>alert('Data berhasil diperbarui!');window.location='index.php?page=jurusanPage.php';</script>";
            exit;
        } else {
            echo "<script>alert('Tidak ada perubahan data atau ID tidak ditemukan.');</script>";
        }
    } else {
        error_log('MYSQL ERROR: ' . $stmt->error);
        echo "<script>alert('Gagal memperbarui data: " . addslashes($stmt->error) . "');</script>";
    }
    $stmt->close();
}
?>
<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1>Edit Jurusan</h1></header>
        <?php if ($jurusan): ?>
        <form method="post" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>ID Jurusan
                <input type="text" name="id_jurusan" value="<?= htmlspecialchars($jurusan['id_jurusan']) ?>" readonly style="background:#f1f1f1;cursor:not-allowed;">
            </label>
            <label>Kode Jurusan
                <input type="text" name="kode_jurusan" value="<?= htmlspecialchars($jurusan['kode_jurusan']) ?>" required>
            </label>
            <label>Nama Jurusan
                <input type="text" name="nama_jurusan" value="<?= htmlspecialchars($jurusan['nama_jurusan']) ?>" required>
            </label>
            <button type="submit">Simpan</button>
            <a href="index.php?page=jurusanPage.php" class="cancel-btn">Batal</a>
        </form>
        <?php else: ?>
            <p class="error-message">Data jurusan tidak ditemukan.</p>
        <?php endif; ?>
    </div>
</main>
