/* Edit Mahasiswa Form Styles */
.edit-mahasiswa-card {
    max-width: 420px;
    margin: 2em auto 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.09);
    padding: 2em 1.5em 2.5em 1.5em;
}
.edit-mahasiswa-card header h1,
.edit-mahasiswa-card header h2 {
    margin-bottom: 1.2em;;
    font-size: 1.45em;
    text-align: center;
}
.edit-mahasiswa-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.2em;
}
.edit-mahasiswa-form label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0;
    text-align: left;
    padding-right: 0;
}
.edit-mahasiswa-form input[type="text"],
.edit-mahasiswa-form input[type="email"],
.edit-mahasiswa-form input[type="password"],
.edit-mahasiswa-form select {
    width: 100%;
    max-width: 400px;
    min-width: 180px;
    margin-top: 0.3em;
    padding: 0.7em 0.9em;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
    background: #f8f9fa;
    transition: border 0.2s;
    box-sizing: border-box;
}
.edit-mahasiswa-form input[type="text"]:focus,
.edit-mahasiswa-form input[type="email"]:focus,
.edit-mahasiswa-form input[type="password"]:focus,
.edit-mahasiswa-form select:focus {
    border: 1.5px solid var(--primary-color);
    outline: none;
    background: #fff;
}
.edit-mahasiswa-form button[type="submit"],
.edit-mahasiswa-form .cancel-btn {
    width: 100%;
    max-width: 320px;
    min-width: 180px;
    margin-left: auto;
    margin-right: auto;
}
.edit-mahasiswa-form button[type="submit"] {
    margin-top: 1.5em;
    font-size: 1.1em;
    padding: 0.7em 0;
    border-radius: 6px;
    background: var(--primary-color);
    color: #fff;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
}
.edit-mahasiswa-form button[type="submit"]:hover {
    background: #0056b3;
}
.edit-mahasiswa-form .cancel-btn {
    background: #aaa;
    margin-top: 0.5em;
    text-align: center;
    font-size: 1.05em;
    padding: 0.7em 0;
    border-radius: 6px;
    color: #fff;
    text-decoration: none;
    display: block;
    transition: background 0.2s;
}
.edit-mahasiswa-form .cancel-btn:hover {
    background: #888;
}
.edit-mahasiswa-card .error-message {
    color: #d33;
    text-align: center;
}
@media (max-width: 600px) {
    .edit-mahasiswa-form {
        grid-template-columns: 1fr;
    }
    .edit-mahasiswa-form label {
        text-align: left;
        padding-right: 0;
    }
    .edit-mahasiswa-form button[type="submit"],
    .edit-mahasiswa-form .cancel-btn {
        grid-column: 1 / 2;
    }
}
