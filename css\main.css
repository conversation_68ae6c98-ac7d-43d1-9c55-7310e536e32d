@import url('root.css');

@import url('login.css');
@import url('register.css');
@import url('forms.css');

@import url('dataTables.css');

/* --- Dashboard Styles for index.php --- */
body {
    background: var(--background-color);
    color: var(--text-color);
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

header {
    background: var(--primary-color);
    color: #fff;
    padding: 1.5rem 0 1rem 0;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    margin-bottom: 1.5rem;
    animation: fadeInDown 0.8s;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    gap: 2rem;
}
nav ul li a {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}
nav ul li a:hover {
    color: #ffd700;
}

/* --- Persistent Active Link Styling for Header Navigation --- */
nav ul li a.active {
    background: #fff;
    color: var(--primary-color) !important;
    border-radius: 6px;
    padding: 0.3em 1em;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
}

/* --- Dashboard Grid Layout --- */
main.dashboard-main {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    gap: 2rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60vh;
    animation: fadeIn 1.2s;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    opacity: 0;
    animation: fadeInUp 0.8s 0.1s forwards;
}
thead th, tbody td {
    text-align: center;
    vertical-align: middle;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.table-container.full-width {
    grid-column: 1 / -1;
    width: 100%;
    max-width: 100%;
}

.table-container {
    width: 100%;
    min-width: 0;
    max-width: 100%;
    background: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    margin-bottom: 0;
    animation: none;
    overflow-x: auto;
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 0.5rem;
}

@media (max-width: 900px) {
    .table-row {
        grid-template-columns: 1fr;
    }
}

footer {
    background: var(--secondary-color);
    color: #fff;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInDown {
    from { opacity: 0; transform: translateY(-40px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Show .table-container after animation */
.table-container {
    opacity: 1;
}