<?php include 'database.php'; ?>
<main class="dashboard-main">
    <div class="table-container full-width">
        <header>
            <h2><PERSON> Jurusan</h2>
        </header>
        <div style="overflow-x:auto;">
        <table class="display" id="jurusanTable">
            <thead>
                <tr>
                    <th><PERSON></th>
                    <th><PERSON><PERSON></th>
                    <th><PERSON><PERSON></th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
            <?php
            $sql = "SELECT id_jurusan, kode_jurusan, nama_jurusan FROM jurusan";
            $result = $conn->query($sql);
            if ($result && $result->num_rows > 0) {
                while($row = $result->fetch_assoc()) {
                    echo "<tr data-id='" . htmlspecialchars($row['id_jurusan']) . "'>";
                    echo "<td>" . htmlspecialchars($row['id_jurusan']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['kode_jurusan']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                    echo "<td>"
                        . "<a href='index.php?page=editJurusan.php&id=" . urlencode($row['id_jurusan']) . "' class='btn-edit' title='Edit'><img src='images/assets/edit.png' alt='Edit' style='width:16px;height:16px;vertical-align:middle;'></a> "
                        . "<a href='#' class='btn-delete-jurusan' title='Delete'><img src='images/assets/eraser.png' alt='Delete' style='width:16px;height:16px;vertical-align:middle;'></a>"
                        . "</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='4'>No data</td></tr>";
            }
            ?>
            </tbody>
        </table>
        </div>
    </div>
</main>

<script>
$(document).ready(function() {
    // Delete jurusan functionality
    $('.btn-delete-jurusan').click(function(e) {
        e.preventDefault();
        const row = $(this).closest('tr');
        const id = row.data('id');
        const nama = row.find('td:nth-child(3)').text();
        
        if (confirm('Apakah Anda yakin ingin menghapus jurusan "' + nama + '"?')) {
            $.ajax({
                url: 'processLogic/deleteJurusan.php',
                type: 'POST',
                data: { id_jurusan: id },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Data jurusan berhasil dihapus!');
                        location.reload();
                    } else {
                        alert('Gagal menghapus data: ' + response.message);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat menghapus data.');
                }
            });
        }
    });
});
</script>
